<?xml version="1.0" encoding="UTF-8"?>
<svg width="500" height="500" viewBox="0 0 500 500" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="500" height="500" fill="url(#heroGradient)"/>
  <circle cx="250" cy="200" r="80" fill="white" opacity="0.1"/>
  <circle cx="150" cy="350" r="60" fill="white" opacity="0.1"/>
  <circle cx="350" cy="350" r="60" fill="white" opacity="0.1"/>
  <rect x="200" y="180" width="100" height="40" rx="20" fill="white" opacity="0.2"/>
  <text x="250" y="205" text-anchor="middle" fill="white" font-family="Arial" font-size="16" font-weight="bold">Innovation</text>
  <defs>
    <linearGradient id="heroGradient" x1="0" y1="0" x2="500" y2="500" gradientUnits="userSpaceOnUse">
      <stop stop-color="#6366F1"/>
      <stop offset="0.5" stop-color="#8B5CF6"/>
      <stop offset="1" stop-color="#6366F1"/>
    </linearGradient>
  </defs>
</svg>
