<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="url(#gameGradient)"/>
  <circle cx="200" cy="150" r="60" fill="white" opacity="0.2"/>
  <rect x="180" y="140" width="40" height="20" rx="4" fill="white" opacity="0.3"/>
  <circle cx="190" cy="130" r="3" fill="white"/>
  <circle cx="210" cy="130" r="3" fill="white"/>
  <text x="200" y="185" text-anchor="middle" fill="white" font-family="Arial" font-size="14" font-weight="bold">GameStorme</text>
  <defs>
    <linearGradient id="gameGradient" x1="0" y1="0" x2="400" y2="300" gradientUnits="userSpaceOnUse">
      <stop stop-color="#7C3AED"/>
      <stop offset="1" stop-color="#A855F7"/>
    </linearGradient>
  </defs>
</svg>
