<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="url(#adukromGradient)"/>
  <circle cx="200" cy="150" r="60" fill="white" opacity="0.2"/>
  <rect x="170" y="130" width="60" height="40" rx="8" fill="white" opacity="0.3"/>
  <text x="200" y="155" text-anchor="middle" fill="white" font-family="Arial" font-size="14" font-weight="bold">Adukrom</text>
  <text x="200" y="175" text-anchor="middle" fill="white" font-family="Arial" font-size="10">Kingdom</text>
  <defs>
    <linearGradient id="adukromGradient" x1="0" y1="0" x2="400" y2="300" gradientUnits="userSpaceOnUse">
      <stop stop-color="#1E40AF"/>
      <stop offset="1" stop-color="#3B82F6"/>
    </linearGradient>
  </defs>
</svg>
