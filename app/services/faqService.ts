interface FAQItem {
  question: string;
  answer: string;
}

interface FAQCategory {
  category: string;
  questions_answers: FAQItem[];
}

interface UnansweredQuestion {
  question: string;
  timestamp: string;
  language: 'en' | 'es';
  context?: string;
}

class FAQService {
  private faqData: { [key: string]: FAQCategory[] } = {};
  private unansweredQuestions: UnansweredQuestion[] = [];

  async loadFAQData(language: 'en' | 'es' = 'en'): Promise<void> {
    try {
      const fileName = language === 'es' ? 'boriqn_faq_es.json' : 'boriqn_faq.json';
      const response = await fetch(`/FAQ/${fileName}`);
      if (response.ok) {
        this.faqData[language] = await response.json();
      }
    } catch (error) {
      console.error(`Error loading FAQ data for ${language}:`, error);
    }
  }

  findAnswer(question: string, language: 'en' | 'es' = 'en'): string | null {
    const faqCategories = this.faqData[language];
    if (!faqCategories) return null;

    const normalizedQuestion = question.toLowerCase().trim();
    
    for (const category of faqCategories) {
      for (const faqItem of category.questions_answers) {
        const normalizedFAQQuestion = faqItem.question.toLowerCase();
        
        // Check for exact match or partial match
        if (normalizedFAQQuestion.includes(normalizedQuestion) || 
            normalizedQuestion.includes(normalizedFAQQuestion) ||
            this.calculateSimilarity(normalizedQuestion, normalizedFAQQuestion) > 0.6) {
          return faqItem.answer;
        }
      }
    }
    
    return null;
  }

  private calculateSimilarity(str1: string, str2: string): number {
    const words1 = str1.split(' ');
    const words2 = str2.split(' ');
    const commonWords = words1.filter(word => words2.includes(word));
    return commonWords.length / Math.max(words1.length, words2.length);
  }

  async saveUnansweredQuestion(question: string, language: 'en' | 'es' = 'en', context?: string): Promise<void> {
    const unansweredQuestion: UnansweredQuestion = {
      question,
      timestamp: new Date().toISOString(),
      language,
      context
    };

    this.unansweredQuestions.push(unansweredQuestion);

    // In a real application, you would save this to a database or file
    // For now, we'll store it in localStorage for persistence
    try {
      const existingQuestions = JSON.parse(localStorage.getItem('boriqn-unanswered-questions') || '[]');
      existingQuestions.push(unansweredQuestion);
      localStorage.setItem('boriqn-unanswered-questions', JSON.stringify(existingQuestions));
    } catch (error) {
      console.error('Error saving unanswered question:', error);
    }
  }

  getSmartResponse(question: string, language: 'en' | 'es' = 'en'): string {
    const message = question.toLowerCase();
    
    // First, try to find answer in FAQ
    const faqAnswer = this.findAnswer(question, language);
    if (faqAnswer) {
      return faqAnswer;
    }

    // If no FAQ answer found, save the question and provide contextual response
    this.saveUnansweredQuestion(question, language);

    // Provide contextual responses based on keywords
    if (language === 'es') {
      return this.getSpanishContextualResponse(message);
    } else {
      return this.getEnglishContextualResponse(message);
    }
  }

  private getEnglishContextualResponse(message: string): string {
    // Business Development responses
    if (message.includes('business') || message.includes('development') || message.includes('growth') || message.includes('strategy')) {
      return "Our Business Development services help accelerate your company's growth through strategic planning and market expansion. We offer market analysis, strategic planning, and growth implementation. For specific details about how we can help your business, please contact <NAME_EMAIL> or call (*************.";
    }
    
    // Blockchain responses
    if (message.includes('blockchain') || message.includes('crypto') || message.includes('smart contract') || message.includes('defi')) {
      return "Our Blockchain Solutions provide cutting-edge implementation for enhanced security and transparency. We specialize in smart contract development, DeFi integration, and cryptocurrency solutions. We can help you leverage blockchain technology to improve your business operations.";
    }
    
    // AI Solutions responses
    if (message.includes('ai') || message.includes('artificial intelligence') || message.includes('agent') || message.includes('companion') || message.includes('automation')) {
      return "Our AI Solutions include advanced artificial intelligence implementations like AI companions, intelligent agents, and process automation. We can help you integrate AI into your business to improve efficiency, customer service, and decision-making processes.";
    }
    
    // Innovation Consulting responses
    if (message.includes('innovation') || message.includes('consulting') || message.includes('technology') || message.includes('digital transformation')) {
      return "Our Innovation Consulting services help you navigate the rapidly evolving technological landscape. We provide technology assessment, digital transformation guidance, and innovation strategy development to keep your business ahead of the competition.";
    }
    
    // Contact/pricing responses
    if (message.includes('contact') || message.includes('price') || message.includes('cost') || message.includes('quote') || message.includes('consultation')) {
      return "For pricing and detailed consultations, please contact <NAME_EMAIL> or call (*************. We're located in Bo. Piedras Blancas, Aguada, PR 00602. We'd be happy to discuss your specific needs and provide a customized solution.";
    }
    
    // Default response
    return "Thank you for your question! I've saved it for our team to review. In the meantime, I can help you learn about our Business Development, Blockchain Solutions, AI Solutions, and Innovation Consulting services. You can also contact us <NAME_EMAIL> or (************* for immediate assistance.";
  }

  private getSpanishContextualResponse(message: string): string {
    // Business Development responses
    if (message.includes('negocio') || message.includes('desarrollo') || message.includes('crecimiento') || message.includes('estrategia') || message.includes('empresa')) {
      return "Nuestros servicios de Desarrollo Empresarial ayudan a acelerar el crecimiento de su empresa mediante planificación estratégica y expansión de mercado. Ofrecemos análisis de mercado, planificación estratégica e implementación de crecimiento. Para detalles específicos sobre cómo podemos ayudar a su negocio, contá<NAME_EMAIL> o llame al (*************.";
    }
    
    // Blockchain responses
    if (message.includes('blockchain') || message.includes('cripto') || message.includes('contrato inteligente') || message.includes('defi') || message.includes('cadena de bloques')) {
      return "Nuestras Soluciones Blockchain proporcionan implementación de vanguardia para mayor seguridad y transparencia. Nos especializamos en desarrollo de contratos inteligentes, integración DeFi y soluciones de criptomonedas. Podemos ayudarle a aprovechar la tecnología blockchain para mejorar sus operaciones comerciales.";
    }
    
    // AI Solutions responses
    if (message.includes('ia') || message.includes('inteligencia artificial') || message.includes('agente') || message.includes('compañero') || message.includes('automatización')) {
      return "Nuestras Soluciones de IA incluyen implementaciones avanzadas de inteligencia artificial como compañeros de IA, agentes inteligentes y automatización de procesos. Podemos ayudarle a integrar IA en su negocio para mejorar la eficiencia, servicio al cliente y procesos de toma de decisiones.";
    }
    
    // Innovation Consulting responses
    if (message.includes('innovación') || message.includes('consultoría') || message.includes('tecnología') || message.includes('transformación digital')) {
      return "Nuestros servicios de Consultoría de Innovación le ayudan a navegar el panorama tecnológico en rápida evolución. Proporcionamos evaluación tecnológica, orientación en transformación digital y desarrollo de estrategias de innovación para mantener su negocio a la vanguardia de la competencia.";
    }
    
    // Contact/pricing responses
    if (message.includes('contacto') || message.includes('precio') || message.includes('costo') || message.includes('cotización') || message.includes('consulta')) {
      return "Para precios y consultas detalladas, contá<NAME_EMAIL> o llame al (*************. Estamos ubicados en Bo. Piedras Blancas, Aguada, PR 00602. Estaremos encantados de discutir sus necesidades específicas y proporcionar una solución personalizada.";
    }
    
    // Default response
    return "¡Gracias por su pregunta! La he guardado para que nuestro equipo la revise. Mientras tanto, puedo ayudarle a conocer nuestros servicios de Desarrollo Empresarial, Soluciones Blockchain, Soluciones de IA y Consultoría de Innovación. También puede contactarnos <NAME_EMAIL> o (************* para asistencia inmediata.";
  }
}

export const faqService = new FAQService();
