@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Montserrat', sans-serif;
  }
}

@layer components {
  .primary-gradient {
    @apply bg-gradient-to-br from-indigo-600 via-purple-600 to-indigo-800;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent;
  }

  .glass-card {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .card-hover {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-2xl;
  }

  .blob {
    @apply rounded-full animate-blob;
  }

  .no-gap {
    @apply m-0 p-0;
  }

  .seamless-footer {
    @apply mt-0 pt-0;
  }
}

/* Custom animations */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

@keyframes blob {
  0% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
  50% { border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%; }
  100% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.3) translateY(-50px);
    opacity: 0;
  }
  50% {
    transform: scale(1.05) translateY(0);
    opacity: 1;
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes slide-up {
  0% {
    transform: translateY(100px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fade-in-scale {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-blob {
  animation: blob 8s ease-in-out infinite;
}

.animate-bounce-in {
  animation: bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-slide-up {
  animation: slide-up 0.8s ease-out;
}

.animate-fade-in-scale {
  animation: fade-in-scale 0.6s ease-out;
}

/* Blockchain animation */
.blockchain-node {
  @apply w-4 h-4 bg-white/30 rounded-full absolute;
}

.blockchain-connection {
  @apply absolute h-0.5 bg-white/20;
}

/* Data packet animation */
@keyframes packet-flow {
  0% { transform: translateX(-100%); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100%); opacity: 0; }
}

.data-packet {
  @apply w-2 h-2 bg-white/60 rounded-full absolute;
  animation: packet-flow 3s ease-in-out infinite;
}

/* Circuit board click animation */
@keyframes circuit-pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  50% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes circuit-lines {
  0% {
    stroke-dasharray: 0 100;
    opacity: 1;
  }
  50% {
    stroke-dasharray: 50 50;
    opacity: 0.8;
  }
  100% {
    stroke-dasharray: 100 0;
    opacity: 0;
  }
}

.circuit-click {
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  width: 100px;
  height: 100px;
  transform: translate(-50%, -50%);
}

.circuit-center {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  background: #6366f1;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: circuit-pulse 0.8s ease-out;
}

.circuit-line {
  stroke: #6366f1;
  stroke-width: 2;
  fill: none;
  animation: circuit-lines 0.8s ease-out;
}

/* Enhanced text contrast */
.text-contrast-high {
  color: #1f2937 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.text-contrast-white {
  color: #ffffff !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.text-contrast-light {
  color: #f9fafb !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
}

/* Enhanced animations */
@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(99, 102, 241, 0.6);
  }
}

@keyframes text-glow {
  0%, 100% {
    text-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
  }
  50% {
    text-shadow: 0 0 20px rgba(99, 102, 241, 0.8);
  }
}

@keyframes card-entrance {
  0% {
    transform: translateY(50px) rotateX(20deg);
    opacity: 0;
  }
  100% {
    transform: translateY(0) rotateX(0deg);
    opacity: 1;
  }
}

.animate-glow-pulse {
  animation: glow-pulse 2s ease-in-out infinite;
}

.animate-text-glow {
  animation: text-glow 3s ease-in-out infinite;
}

.animate-card-entrance {
  animation: card-entrance 0.8s ease-out;
}
