'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';

export default function AboutPage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  return (
    <div className="min-h-screen flex flex-col bg-white">
      <div className="flex-grow">
        {/* Header */}
        <nav className="fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md border-b border-gray-200">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-2">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-10 h-10 flex items-center justify-center">
                  <Image src="/logo/coquilogo.png" alt="Boriqn Logo" width={32} height={32} className="h-8 w-auto" />
                </div>
                <span className="text-xl font-bold text-gray-900">Boriqn</span>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <Link href="/#home" className="text-gray-700 hover:text-indigo-600 transition-colors relative group">
                Home
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 transition-all group-hover:w-full"></span>
              </Link>
              <Link href="/#services" className="text-gray-700 hover:text-indigo-600 transition-colors relative group">
                Services
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 transition-all group-hover:w-full"></span>
              </Link>
              <Link href="/#tokenization" className="text-gray-700 hover:text-indigo-600 transition-colors relative group">
                Tokenization
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 transition-all group-hover:w-full"></span>
              </Link>
              <Link href="/#products" className="text-gray-700 hover:text-indigo-600 transition-colors relative group">
                Products
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 transition-all group-hover:w-full"></span>
              </Link>
              <Link href="/about" className="text-indigo-600 font-medium relative group">
                About
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-indigo-600"></span>
              </Link>
              <Link href="/#contact" className="text-gray-700 hover:text-indigo-600 transition-colors relative group">
                Contact
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 transition-all group-hover:w-full"></span>
              </Link>
              <Link href="/#contact" className="primary-gradient text-white px-6 py-2 rounded-lg hover:opacity-90 transition-opacity">
                Get Started
              </Link>
            </div>

            {/* Mobile menu button */}
            <button
              className="md:hidden p-2"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden py-4 border-t border-gray-200">
              <div className="flex flex-col space-y-4">
                <Link href="/#home" className="text-gray-700 hover:text-indigo-600 transition-colors">Home</Link>
                <Link href="/#services" className="text-gray-700 hover:text-indigo-600 transition-colors">Services</Link>
                <Link href="/#tokenization" className="text-gray-700 hover:text-indigo-600 transition-colors">Tokenization</Link>
                <Link href="/#products" className="text-gray-700 hover:text-indigo-600 transition-colors">Products</Link>
                <Link href="/about" className="text-indigo-600 font-medium">About</Link>
                <Link href="/#contact" className="text-gray-700 hover:text-indigo-600 transition-colors">Contact</Link>
                <Link href="/#contact" className="primary-gradient text-white px-6 py-2 rounded-lg hover:opacity-90 transition-opacity inline-block text-center">
                  Get Started
                </Link>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative pt-40 pb-20 bg-gradient-to-br from-indigo-900 to-purple-900 text-white overflow-hidden">
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium mb-6"
            >
              Our Story & Values
            </motion.div>
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-4xl md:text-6xl font-bold mb-6 leading-tight"
            >
              Empowering Innovation <br/>
              <span className="text-indigo-200">Across Puerto Rico</span>
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-xl text-indigo-100 max-w-3xl mx-auto mb-8"
            >
              At Boriqn, we are dedicated to transforming businesses through cutting-edge blockchain technology, strategic business development, and innovative digital solutions that drive growth and success.
            </motion.p>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <div className="inline-block px-3 py-1 bg-indigo-100 text-indigo-600 rounded-full font-medium text-sm mb-4">
                Our Mission
              </div>
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Driving Digital Transformation
              </h2>
              <p className="text-xl text-gray-700 leading-relaxed">
                Based in Puerto Rico, Boriqn specializes in blockchain technology, business development, and digital innovation. We empower businesses to harness the transformative power of emerging technologies, creating sustainable growth opportunities while contributing to the economic development of Puerto Rico and the broader Caribbean region.
              </p>
            </motion.div>
          </div>

          {/* Values & Story */}
          <div className="grid md:grid-cols-2 gap-8 mb-20">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl p-8 shadow-lg"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold mb-4 text-gray-900">Our Values</h3>
              <p className="text-gray-700 leading-relaxed">
                At Boriqn, our mission is fueled by the core values of <span className="font-semibold text-indigo-600">innovation</span>, <span className="font-semibold text-purple-600">collaboration</span>, and <span className="font-semibold text-indigo-600">economic empowerment</span>. We leverage blockchain technology, smart contracts, and tokenization to create transformative solutions that unlock new opportunities for businesses and communities across Puerto Rico and beyond.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-8 shadow-lg"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold mb-4 text-gray-900">Our Story</h3>
              <p className="text-gray-700 leading-relaxed">
                Founded in Puerto Rico with a vision to bridge traditional business with emerging blockchain technologies, Boriqn has become a catalyst for digital transformation in the Caribbean. Our team combines deep technical expertise with local market knowledge, enabling us to deliver solutions that are both globally competitive and locally relevant. We&apos;re committed to fostering innovation while creating sustainable economic opportunities for our community.
              </p>
            </motion.div>
          </div>

          {/* CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl p-8 md:p-12 text-center text-white"
          >
            <h2 className="text-3xl font-bold mb-4">Ready to Transform Your Business?</h2>
            <p className="text-xl text-indigo-100 mb-8 max-w-2xl mx-auto">
              Discover how Boriqn&apos;s innovative solutions can help your business thrive in the digital age. Let&apos;s build the future together.
            </p>
            <Link
              href="/#contact"
              className="inline-block bg-white text-indigo-600 font-semibold px-8 py-3 rounded-lg hover:bg-opacity-90 transition-all duration-300"
            >
              Get in Touch
            </Link>
          </motion.div>
        </div>
      </section>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Image src="/logo/coquilogo.png" alt="Boriqn Logo" width={32} height={32} className="h-8 w-auto" />
                <span className="text-xl font-bold">Boriqn</span>
              </div>
              <p className="text-gray-400 text-sm">
                Empowering innovation through blockchain technology and strategic business solutions in Puerto Rico and beyond.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
              <ul className="space-y-2">
                <li><Link href="/#home" className="text-gray-400 hover:text-white transition-colors">Home</Link></li>
                <li><Link href="/#services" className="text-gray-400 hover:text-white transition-colors">Services</Link></li>
                <li><Link href="/#tokenization" className="text-gray-400 hover:text-white transition-colors">Tokenization</Link></li>
                <li><Link href="/#products" className="text-gray-400 hover:text-white transition-colors">Products</Link></li>
                <li><Link href="/about" className="text-gray-400 hover:text-white transition-colors">About Us</Link></li>
                <li><Link href="/#contact" className="text-gray-400 hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Services</h3>
              <ul className="space-y-2">
                <li><Link href="/#services" className="text-gray-400 hover:text-white transition-colors">Business Development</Link></li>
                <li><Link href="/#services" className="text-gray-400 hover:text-white transition-colors">Blockchain Solutions</Link></li>
                <li><Link href="/#tokenization" className="text-gray-400 hover:text-white transition-colors">Asset Tokenization</Link></li>
                <li><Link href="/#products" className="text-gray-400 hover:text-white transition-colors">Our Products</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
              <address className="text-gray-400 not-italic">
                <p className="mb-2">Bo. Piedras Blancas</p>
                <p className="mb-2">Aguada, PR 00602</p>
                <p className="mb-2">Email: <EMAIL></p>
                <p>Phone: (*************</p>
              </address>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
            <p>&copy; {new Date().getFullYear()} Boriqn. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
