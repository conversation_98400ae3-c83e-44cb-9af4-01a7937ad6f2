import type { Metada<PERSON> } from 'next'
import { Montserrat } from 'next/font/google'
import './globals.css'
import CookieConsent from './components/CookieConsent'

const montserrat = Montserrat({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Boriqn - Blockchain Innovation',
  description: 'Empowering Puerto Rico and beyond through innovation, blockchain technology, and strategic business development solutions.',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="h-full">
      <body className={`${montserrat.className} flex flex-col min-h-screen bg-white`}>
        <div className="flex-grow">
          {children}
        </div>
        <CookieConsent />
      </body>
    </html>
  )
}
