import type { <PERSON>ada<PERSON> } from 'next'
import { Montser<PERSON> } from 'next/font/google'
import './globals.css'
import CookieConsent from './components/CookieConsent'
import AIAgent from './components/AIAgent'
import { LanguageProvider } from './contexts/LanguageContext'

const montserrat = Montserrat({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Boriqn - Blockchain Innovation',
  description: 'Empowering Puerto Rico and beyond through innovation, blockchain technology, and strategic business development solutions.',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="h-full">
      <body className={`${montserrat.className} min-h-screen bg-white`}>
        <LanguageProvider>
          {children}
          <CookieConsent />
          <AIAgent />
        </LanguageProvider>
      </body>
    </html>
  )
}
