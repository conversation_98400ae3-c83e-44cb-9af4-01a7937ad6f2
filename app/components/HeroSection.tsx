'use client';

import BlockchainAnimation from './BlockchainAnimation';

export default function HeroSection() {
  return (
    <section id="home" className="pt-20 pb-16 primary-gradient text-white relative overflow-hidden">
      <div className="absolute inset-0 opacity-10">
        <BlockchainAnimation />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          <div className="space-y-8 animate-slide-up">
            <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight mb-6 text-contrast-white">
              Empowering Puerto Rico and Beyond through <span className="bg-gradient-to-r from-yellow-200 via-white to-yellow-200 bg-clip-text text-transparent animate-text-glow">Innovation, Blockchain, and Business Development</span>
            </h1>

            <p className="text-lg text-white/90 mb-8 max-w-xl text-contrast-light">
              Our expertise lies in developing and implementing cutting-edge solutions that streamline your business processes, enhance productivity, and drive growth. We work with a wide range of clients across industries, from small startups to large corporations, to deliver customized IT services that fit their unique needs.
            </p>

            <div className="flex flex-col sm:flex-row gap-4">
              <a href="#contact" className="bg-white text-indigo-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 hover:shadow-lg text-center animate-fade-in-scale" style={{animationDelay: '0.6s'}}>
                Start Your Project
              </a>
              <a href="#services" className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-indigo-600 transition-all duration-300 transform hover:scale-105 text-center animate-fade-in-scale" style={{animationDelay: '0.8s'}}>
                Learn More
              </a>
            </div>
          </div>

          <div className="w-full h-[500px] rounded-2xl overflow-hidden">
            <img
              src="/webdev/heroimage.jpg"
              alt="Hero"
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
