'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';

export default function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md border-b border-gray-200">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-10 h-10 flex items-center justify-center">
              <Image src="/logo/coquilogo.svg" alt="Boriqn Logo" width={32} height={32} className="h-8 w-auto" />
            </div>
            <span className="text-xl font-bold text-gray-900">Boriqn</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <a href="#home" className="text-gray-700 hover:text-indigo-600 transition-colors relative group">
              Home
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 transition-all group-hover:w-full"></span>
            </a>
            <a href="#services" className="text-gray-700 hover:text-indigo-600 transition-colors relative group">
              Services
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 transition-all group-hover:w-full"></span>
            </a>
            <a href="#tokenization" className="text-gray-700 hover:text-indigo-600 transition-colors relative group">
              Tokenization
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 transition-all group-hover:w-full"></span>
            </a>
            <a href="#products" className="text-gray-700 hover:text-indigo-600 transition-colors relative group">
              Products
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 transition-all group-hover:w-full"></span>
            </a>
            <Link href="/about" className="text-gray-700 hover:text-indigo-600 transition-colors relative group">
              About
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 transition-all group-hover:w-full"></span>
            </Link>
            <a href="#contact" className="text-gray-700 hover:text-indigo-600 transition-colors relative group">
              Contact
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 transition-all group-hover:w-full"></span>
            </a>
            <a href="#contact" className="primary-gradient text-white px-6 py-2 rounded-lg hover:opacity-90 transition-opacity">
              Get Started
            </a>
          </div>

          {/* Mobile menu button */}
          <button
            className="md:hidden p-2"
            onClick={toggleMenu}
            aria-label="Toggle menu"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200">
            <div className="flex flex-col space-y-4">
              <a href="#home" className="text-gray-700 hover:text-indigo-600 transition-colors" onClick={toggleMenu}>Home</a>
              <a href="#services" className="text-gray-700 hover:text-indigo-600 transition-colors" onClick={toggleMenu}>Services</a>
              <a href="#tokenization" className="text-gray-700 hover:text-indigo-600 transition-colors" onClick={toggleMenu}>Tokenization</a>
              <a href="#products" className="text-gray-700 hover:text-indigo-600 transition-colors" onClick={toggleMenu}>Products</a>
              <Link href="/about" className="text-gray-700 hover:text-indigo-600 transition-colors" onClick={toggleMenu}>About</Link>
              <a href="#contact" className="text-gray-700 hover:text-indigo-600 transition-colors" onClick={toggleMenu}>Contact</a>
              <a href="#contact" className="primary-gradient text-white px-6 py-2 rounded-lg hover:opacity-90 transition-opacity inline-block text-center">
                Get Started
              </a>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
