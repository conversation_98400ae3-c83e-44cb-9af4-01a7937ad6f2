const businesses = [
  {
    name: 'Adukrom Kingdom',
    description: 'A comprehensive digital platform that brings together innovative technologies and business solutions to create new opportunities in the digital economy.',
    url: 'https://www.adukromkingdom.com/adukrom',
    image: '/webdev/adukrom.png',
    buttonText: 'Visit Adukrom',
    tags: ['Digital Platform', 'Innovation', 'Technology']
  },
  {
    name: 'GameStorme',
    description: 'An innovative gaming platform that brings together gamers and developers in a vibrant community.',
    url: 'https://gamestorme-9c0b68273ab5.herokuapp.com/',
    image: '/webdev/gamestorme.png',
    buttonText: 'Play GameStorme',
    tags: ['Gaming', 'Community', 'Entertainment']
  },
  {
    name: 'Boss Privé',
    description: 'Exclusive members-only platform for high-end business networking and luxury services.',
    url: 'https://bossprive-45c6bcb7cf35.herokuapp.com/',
    image: '/webdev/bossprive.png',
    buttonText: 'Join <PERSON>v<PERSON>',
    tags: ['Networking', 'Luxury', 'Exclusive']
  }
];

import Image from 'next/image';

export default function ProductsSection() {
  return (
    <section id="products" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="inline-block px-3 py-1 bg-indigo-100 text-indigo-600 rounded-full font-medium text-sm mb-4">
            Our Businesses
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
            Discover Our Innovative Platforms
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore our portfolio of cutting-edge digital platforms and services.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {businesses.map((business, index) => (
            <div key={index} className="bg-white rounded-2xl shadow-xl overflow-hidden transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 flex flex-col h-full">
              {/* Image only - no overlays */}
              <div className="h-64 flex-shrink-0">
                <Image
                  src={business.image}
                  alt={business.name}
                  width={400}
                  height={300}
                  className="w-full h-full object-cover"
                />
              </div>

              <div className="p-6 flex flex-col flex-grow">
                {/* Company name */}
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {business.name}
                </h3>

                {/* Description */}
                <p className="text-gray-600 mb-6">{business.description}</p>

                {/* Spacer to push content up */}
                <div className="mt-auto">
                  {/* Tags above button */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {business.tags.map((tag, i) => (
                      <span key={i} className="text-xs font-medium px-3 py-1 bg-gray-100 text-gray-700 rounded-full">
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Button at the bottom */}
                  <a
                    href={business.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block w-full py-3 px-6 rounded-lg font-medium text-white text-center bg-gradient-to-r from-indigo-600 to-purple-600 hover:opacity-90 transition-opacity"
                  >
                    {business.buttonText}
                    <svg className="inline-block w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
