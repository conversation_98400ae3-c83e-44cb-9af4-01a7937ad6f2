'use client';

import { useEffect, useRef } from 'react';

export default function BlockchainAnimation() {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Clear any existing content
    container.innerHTML = '';
    
    // Create nodes
    const nodes = [
      { x: 20, y: 30 },
      { x: 80, y: 20 },
      { x: 50, y: 70 },
      { x: 40, y: 50 },
      { x: 70, y: 40 }
    ];

    // Create SVG element
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', '100%');
    svg.setAttribute('height', '100%');
    svg.setAttribute('viewBox', '0 0 100 100');
    svg.style.position = 'absolute';
    svg.style.top = '0';
    svg.style.left = '0';
    svg.style.zIndex = '0';
    svg.style.opacity = '0.3';
    svg.style.overflow = 'visible';

    // Add nodes and connections
    nodes.forEach((node, i) => {
      // Draw connections to other nodes
      nodes.forEach((otherNode, j) => {
        if (i < j) { // Only draw each connection once
          const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
          line.setAttribute('x1', node.x.toString());
          line.setAttribute('y1', node.y.toString());
          line.setAttribute('x2', otherNode.x.toString());
          line.setAttribute('y2', otherNode.y.toString());
          line.setAttribute('stroke', 'currentColor');
          line.setAttribute('stroke-width', '0.5');
          line.setAttribute('stroke-dasharray', '2,2');
          svg.appendChild(line);
        }
      });

      // Draw node
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      circle.setAttribute('cx', node.x.toString());
      circle.setAttribute('cy', node.y.toString());
      circle.setAttribute('r', '2');
      circle.setAttribute('fill', 'currentColor');
      svg.appendChild(circle);
    });

    container.appendChild(svg);

    // Animation
    let animationFrame: number;
    let angle = 0;
    
    const animate = () => {
      if (!container) return;
      
      angle += 0.002;
      const scale = 1 + Math.sin(angle) * 0.1;
      
      svg.style.transform = `scale(${scale})`;
      animationFrame = requestAnimationFrame(animate);
    };
    
    animationFrame = requestAnimationFrame(animate);
    
    return () => {
      cancelAnimationFrame(animationFrame);
      if (container.contains(svg)) {
        container.removeChild(svg);
      }
    };
  }, []);

  return (
    <div 
      ref={containerRef} 
      id="blockchain-animation" 
      className="absolute inset-0 pointer-events-none"
      aria-hidden="true"
    />
  );
}
