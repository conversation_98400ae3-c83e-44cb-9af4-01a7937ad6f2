'use client';

import { useState, useEffect } from 'react';

export default function CookieConsent() {
  const [mounted, setMounted] = useState(false);
  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    setMounted(true);
    const consent = window.localStorage?.getItem('cookieConsent');
    if (consent !== 'accepted') {
      setShowBanner(true);
    }
  }, []);

  const acceptCookies = () => {
    if (typeof window !== 'undefined') {
      window.localStorage?.setItem('cookieConsent', 'accepted');
    }
    setShowBanner(false);
  };

  if (!mounted || !showBanner) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-gray-800 text-white p-4 z-50">
      <div className="container mx-auto flex flex-col md:flex-row items-center justify-between">
        <div className="mb-4 md:mb-0 md:mr-4">
          <p className="text-sm">
            We use cookies to enhance your experience on our website. By continuing to browse, you agree to our use of cookies.
            Learn more in our <a href="/cookie-policy" className="text-indigo-300 hover:text-white underline">Cookie Policy</a>.
          </p>
        </div>
        <div className="flex space-x-4">
          <button 
            onClick={acceptCookies}
            className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
          >
            Accept All
          </button>
          <button 
            onClick={() => setShowBanner(false)}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
          >
            Reject
          </button>
        </div>
      </div>
    </div>
  );
}
