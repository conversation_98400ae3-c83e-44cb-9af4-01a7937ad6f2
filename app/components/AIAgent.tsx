'use client';

import { useState, useRef, useEffect } from 'react';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

export default function AIAgent() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hello! I'm <PERSON><PERSON><PERSON><PERSON>'s AI assistant. I can help you learn about our services including Business Development, Blockchain Solutions, AI Solutions, and Innovation Consulting. What would you like to know?",
      isUser: false,
      timestamp: new Date()
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const getAIResponse = (userMessage: string): string => {
    const message = userMessage.toLowerCase();
    
    // Business Development responses
    if (message.includes('business development') || message.includes('business') || message.includes('growth')) {
      return "Our Business Development services help accelerate your company's growth through strategic planning and market expansion. We offer market analysis & strategy, growth planning, and partnership development. We work with businesses of all sizes to identify opportunities and implement growth strategies.";
    }
    
    // Blockchain responses
    if (message.includes('blockchain') || message.includes('crypto') || message.includes('smart contract')) {
      return "Our Blockchain Solutions provide cutting-edge implementation for enhanced security and transparency. We specialize in smart contract development, DApp development, and blockchain integration. We can help you leverage blockchain technology to improve your business operations and create new opportunities.";
    }
    
    // AI Solutions responses
    if (message.includes('ai') || message.includes('artificial intelligence') || message.includes('agent') || message.includes('companion')) {
      return "Our AI Solutions include advanced artificial intelligence implementations like AI companions, intelligent agents, and process automation. We can help you integrate AI into your business to improve efficiency, customer service, and decision-making processes.";
    }
    
    // Innovation Consulting responses
    if (message.includes('innovation') || message.includes('consulting') || message.includes('technology') || message.includes('digital transformation')) {
      return "Our Innovation Consulting services help you navigate the rapidly evolving technological landscape. We provide technology assessment, digital transformation guidance, and innovation strategy development to keep your business ahead of the competition.";
    }
    
    // Tokenization responses
    if (message.includes('tokenization') || message.includes('token')) {
      return "We offer comprehensive tokenization solutions to help businesses leverage blockchain technology for asset digitization, fractional ownership, and new business models. Our tokenization services can transform how you manage and trade assets.";
    }
    
    // Contact/pricing responses
    if (message.includes('contact') || message.includes('price') || message.includes('cost') || message.includes('quote')) {
      return "For pricing and detailed consultations, please contact <NAME_EMAIL> or call (939) 349-3622. We're located in Bo. Piedras Blancas, Aguada, PR 00602. We'd be happy to discuss your specific needs and provide a customized solution.";
    }
    
    // Location responses
    if (message.includes('location') || message.includes('where') || message.includes('puerto rico')) {
      return "We're based in Puerto Rico, specifically in Bo. Piedras Blancas, Aguada, PR 00602. While we're rooted in Puerto Rico, we serve clients globally and are committed to empowering businesses both locally and internationally.";
    }
    
    // General greetings
    if (message.includes('hello') || message.includes('hi') || message.includes('hey')) {
      return "Hello! Welcome to Boriqn. We're a technology company specializing in Business Development, Blockchain Solutions, AI Solutions, and Innovation Consulting. How can I help you today?";
    }
    
    // Default response
    return "I'd be happy to help you learn more about Boriqn's services! We specialize in Business Development, Blockchain Solutions, AI Solutions, and Innovation Consulting. You can also ask me about our tokenization services, pricing, or how to contact us. What specific area interests you most?";
  };

  const handleSendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    // Simulate AI thinking time
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        text: getAIResponse(inputText),
        isUser: false,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000); // Random delay between 1-2 seconds
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Chat Button */}
      <button
        onClick={() => setIsOpen(true)}
        className={`fixed bottom-6 right-6 z-50 w-16 h-16 primary-gradient rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 ${isOpen ? 'hidden' : 'flex'} items-center justify-center`}
      >
        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      </button>

      {/* Chat Window */}
      {isOpen && (
        <div className="fixed bottom-6 right-6 z-50 w-96 h-[500px] bg-white rounded-2xl shadow-2xl border border-gray-200 flex flex-col">
          {/* Header */}
          <div className="primary-gradient text-white p-4 rounded-t-2xl flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold">Boriqn AI Assistant</h3>
                <p className="text-xs text-white/80">Online</p>
              </div>
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="text-white/80 hover:text-white transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] p-3 rounded-2xl ${
                    message.isUser
                      ? 'bg-indigo-600 text-white'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  <p className="text-sm">{message.text}</p>
                  <p className={`text-xs mt-1 ${message.isUser ? 'text-indigo-200' : 'text-gray-500'}`}>
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </p>
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 text-gray-800 p-3 rounded-2xl">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex space-x-2">
              <input
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask me about our services..."
                className="flex-1 p-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputText.trim() || isTyping}
                className="primary-gradient text-white p-3 rounded-xl hover:opacity-90 transition-opacity disabled:opacity-50"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
