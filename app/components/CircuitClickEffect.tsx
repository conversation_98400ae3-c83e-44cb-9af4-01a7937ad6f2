'use client'

import { useEffect } from 'react'

export default function CircuitClickEffect() {
  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      // Create circuit board animation at click position
      const circuitElement = document.createElement('div')
      circuitElement.className = 'circuit-click'
      circuitElement.style.left = `${e.clientX}px`
      circuitElement.style.top = `${e.clientY}px`

      // Create the circuit board SVG
      circuitElement.innerHTML = `
        <svg width="100" height="100" viewBox="0 0 100 100">
          <!-- Central node -->
          <circle cx="50" cy="50" r="4" fill="#6366f1" class="circuit-center" />
          
          <!-- Circuit lines -->
          <line x1="50" y1="50" x2="20" y2="20" class="circuit-line" />
          <line x1="50" y1="50" x2="80" y2="20" class="circuit-line" />
          <line x1="50" y1="50" x2="20" y2="80" class="circuit-line" />
          <line x1="50" y1="50" x2="80" y2="80" class="circuit-line" />
          <line x1="50" y1="50" x2="50" y2="10" class="circuit-line" />
          <line x1="50" y1="50" x2="50" y2="90" class="circuit-line" />
          <line x1="50" y1="50" x2="10" y2="50" class="circuit-line" />
          <line x1="50" y1="50" x2="90" y2="50" class="circuit-line" />
          
          <!-- Corner nodes -->
          <circle cx="20" cy="20" r="2" fill="#6366f1" opacity="0.8" />
          <circle cx="80" cy="20" r="2" fill="#6366f1" opacity="0.8" />
          <circle cx="20" cy="80" r="2" fill="#6366f1" opacity="0.8" />
          <circle cx="80" cy="80" r="2" fill="#6366f1" opacity="0.8" />
          
          <!-- Edge nodes -->
          <circle cx="50" cy="10" r="2" fill="#6366f1" opacity="0.8" />
          <circle cx="50" cy="90" r="2" fill="#6366f1" opacity="0.8" />
          <circle cx="10" cy="50" r="2" fill="#6366f1" opacity="0.8" />
          <circle cx="90" cy="50" r="2" fill="#6366f1" opacity="0.8" />
          
          <!-- Additional circuit paths -->
          <path d="M20,20 L30,15 L40,25 L50,20" stroke="#6366f1" stroke-width="1" fill="none" class="circuit-line" opacity="0.6" />
          <path d="M80,20 L70,15 L60,25 L50,20" stroke="#6366f1" stroke-width="1" fill="none" class="circuit-line" opacity="0.6" />
          <path d="M20,80 L30,85 L40,75 L50,80" stroke="#6366f1" stroke-width="1" fill="none" class="circuit-line" opacity="0.6" />
          <path d="M80,80 L70,85 L60,75 L50,80" stroke="#6366f1" stroke-width="1" fill="none" class="circuit-line" opacity="0.6" />
        </svg>
      `

      document.body.appendChild(circuitElement)

      // Remove the element after animation completes
      setTimeout(() => {
        if (circuitElement.parentNode) {
          circuitElement.parentNode.removeChild(circuitElement)
        }
      }, 800)
    }

    // Add click event listener to the document
    document.addEventListener('click', handleClick)

    // Cleanup function
    return () => {
      document.removeEventListener('click', handleClick)
    }
  }, [])

  return null // This component doesn't render anything visible
}
