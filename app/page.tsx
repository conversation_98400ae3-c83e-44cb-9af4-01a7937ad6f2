'use client';

import Navigation from './components/Navigation';
import HeroSection from './components/HeroSection';
import ServicesSection from './components/ServicesSection';
import WhyChooseSection from './components/WhyChooseSection';
import TokenizationSection from './components/TokenizationSection';
import ProductsSection from './components/ProductsSection';
import ContactSection from './components/ContactSection';
import Footer from './components/Footer';

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-grow">
        {/* Navigation */}
        <Navigation />

        {/* Hero Section */}
        <HeroSection />

        {/* Services Section */}
        <ServicesSection />

        {/* Why Choose Boriqn */}
        <WhyChooseSection />

        {/* Tokenization Section */}
        <TokenizationSection />

        {/* Products Section */}
        <section id="products" className="py-20 bg-gray-50">
          <ProductsSection />
        </section>

        {/* Contact Section */}
        <ContactSection />
      </div>

      {/* Footer */}
      <Footer />
    </div>
  )
}
