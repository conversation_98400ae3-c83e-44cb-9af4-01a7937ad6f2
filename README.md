# Boriqn - Innovation & Blockchain Solutions

A modern, responsive website for Boriqn, built with Next.js and featuring beautiful animations, gradients, and glass morphism effects.

## Features

- Modern Next.js application with React components
- Responsive design that works on all devices
- Interactive UI elements with micro-interactions and circuit board animations
- Glass morphism effects and gradient designs
- Optimized for Heroku deployment
- TypeScript for type safety
- Tailwind CSS for styling

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm (v9 or higher)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/joelgriiyo/boriqn2.git
cd boriqn2
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### Building for Production

```bash
npm run build
npm start
```

### Deployment

This application is configured for Heroku deployment with the included `Procfile`.

#### Deploy to Heroku (Recommended):

1. **Create a new Heroku app:**
   ```bash
   heroku create your-app-name
   ```

2. **Connect to this repository:**
   ```bash
   git remote add heroku https://git.heroku.com/your-app-name.git
   ```

3. **Deploy:**
   ```bash
   git push heroku main
   ```

4. **Or deploy via GitHub integration:**
   - Go to your Heroku dashboard
   - Connect your GitHub repository
   - Enable automatic deploys from the main branch

The app will automatically:
- Install dependencies (`npm install`)
- Build the application (`npm run build`)
- Start the production server (`npm start`)

#### Environment Configuration:
- **Node.js Version:** 18.x (specified in package.json)
- **Build Command:** `npm run build` (automatically triggered by `heroku-postbuild`)
- **Start Command:** `npm start` (specified in Procfile)

## Project Structure

```
boriqn2/
├── app/                    # Next.js app directory
│   ├── components/         # React components
│   │   ├── CircuitClickEffect.tsx
│   │   ├── ContactSection.tsx
│   │   └── ProductsSection.tsx
│   ├── globals.css         # Global styles
│   ├── layout.tsx          # Root layout
│   └── page.tsx           # Home page
├── public/                 # Static assets (auto-generated)
├── .gitignore             # Git ignore rules
├── next.config.js         # Next.js configuration
├── package.json           # Dependencies and scripts
├── Procfile              # Heroku deployment configuration
├── tailwind.config.js    # Tailwind CSS configuration
└── tsconfig.json         # TypeScript configuration
```

## Technologies Used

- **Next.js 14** - React framework with App Router
- **React 18** - UI library
- **TypeScript** - Type safety
- **Tailwind CSS** - Utility-first CSS framework
- **PostCSS** - CSS processing
- **ESLint** - Code linting

## Development

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Environment Variables

No environment variables are required for basic functionality. The application is configured to work out of the box.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request
