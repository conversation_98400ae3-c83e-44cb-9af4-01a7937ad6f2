/** @type {import('next').NextConfig} */
const nextConfig = {
  // Optimized for Heroku deployment
  images: {
    unoptimized: true, // Required for static exports
    domains: [], // Add any external domains if you're loading images from external sources
    path: '/_next/image',
    loader: 'default',
  },
  compress: true,
  poweredByHeader: false,
  generateEtags: false,
  trailingSlash: true, // Changed to true for better static hosting compatibility
  swcMinify: true,
  // Ensure static assets are served correctly
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
  // Add base path if your app is not served from the root
  // basePath: '',
  // Add asset prefix if needed for CDN or subfolder deployment
  // assetPrefix: process.env.NODE_ENV === 'production' ? 'https://your-cdn-url.com' : '',
};

module.exports = nextConfig;
